import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus } from "./types";
import {
  hasAvailableBalance,
  lockFunds,
  unlockFunds,
  spendLockedFunds,
  updateUserBalance,
} from "./balance-service";
import {
  applyPurchaseFeeWithReferral,
  getAppConfig,
  applyFeeToMarketplaceRevenue,
} from "./fee-service";
import { getNextCounterValue } from "./counter-service";
import { verifyBotToken } from "./bot-auth-service";
import {
  DEFAULT_SELLER_LOCK_PERCENTAGE,
  FIXED_REJECTION_FEE_TON,
  DEFAULT_REJECT_ORDER_FEE_BPS,
  BPS_DIVISOR,
  DEFAULT_BUYER_LOCK_PERCENTAGE,
} from "./constants";

export const createOrderAsSeller = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { sellerId, productId, collectionId, amount, owned_gift_id } = data;

    if (!sellerId || !productId || !collectionId || !amount || amount <= 0) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "sellerId, productId, collectionId, and valid amount are required."
      );
    }

    if (context.auth.uid !== sellerId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You can only create orders for yourself as seller."
      );
    }

    try {
      const db = admin.firestore();

      // Get collection and validate floor price
      const collectionDoc = await db
        .collection("collections")
        .doc(collectionId)
        .get();
      if (!collectionDoc.exists) {
        throw new functions.https.HttpsError(
          "not-found",
          "Collection not found."
        );
      }

      const collection = {
        id: collectionDoc.id,
        ...collectionDoc.data(),
      } as any;
      if (collection.floorPrice && amount < collection.floorPrice) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          `Order amount ${amount} TON is below collection floor price of ${collection.floorPrice} TON.`
        );
      }

      // Get seller lock percentage from app config
      const config = await getAppConfig();
      const sellerLockPercentage =
        config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;
      const lockedAmount = amount * sellerLockPercentage;

      // Check seller balance for configurable percentage of order amount
      const hasBalance = await hasAvailableBalance(sellerId, lockedAmount);
      if (!hasBalance) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          `Insufficient available balance to create order. Need ${lockedAmount} TON (${
            sellerLockPercentage * 100
          }% of order amount).`
        );
      }

      // Lock seller percentage of order amount for seller
      await lockFunds(sellerId, lockedAmount);

      // Get next order number
      const orderNumber = await getNextCounterValue("order_number");

      const orderData: Omit<OrderEntity, "id"> = {
        number: orderNumber,
        sellerId,
        productId,
        collectionId,
        amount,
        status: OrderStatus.ACTIVE,
        owned_gift_id,
        createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
        updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
      };

      const orderRef = await db.collection("orders").add(orderData);

      return {
        success: true,
        orderId: orderRef.id,
        message: `Order created successfully with ${lockedAmount} TON locked (${
          sellerLockPercentage * 100
        }% of ${amount} TON order)`,
      };
    } catch (error) {
      console.error("Error creating order:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while creating order."
      );
    }
  }
);

export const createOrderAsBuyer = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { buyerId, productId, collectionId, amount, owned_gift_id } = data;

    if (!buyerId || !productId || !collectionId || !amount || amount <= 0) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "buyerId, productId, collectionId, and valid amount are required."
      );
    }

    if (context.auth.uid !== buyerId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You can only create orders for yourself as buyer."
      );
    }

    try {
      const db = admin.firestore();

      // Get collection to validate floor price
      const collectionDoc = await db
        .collection("collections")
        .doc(collectionId)
        .get();
      if (!collectionDoc.exists) {
        throw new functions.https.HttpsError(
          "not-found",
          "Collection not found."
        );
      }

      const collection = collectionDoc.data();
      if (amount < collection?.floorPrice) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          `Order amount must be at least ${collection?.floorPrice} TON (collection floor price).`
        );
      }

      // Get buyer lock percentage from app config
      const config = await getAppConfig();
      const buyerLockPercentage =
        config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
      const lockedAmount = amount * buyerLockPercentage;

      // Check if buyer has sufficient balance
      const hasBalance = await hasAvailableBalance(buyerId, lockedAmount);
      if (!hasBalance) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          `Insufficient available balance to create order. Need ${lockedAmount} TON (${
            buyerLockPercentage * 100
          }% of order amount).`
        );
      }

      // Lock buyer percentage of order amount for buyer
      await lockFunds(buyerId, lockedAmount);

      // Get next order number
      const orderNumber = await getNextCounterValue("order_number");

      const orderData: Omit<OrderEntity, "id"> = {
        number: orderNumber,
        buyerId,
        productId,
        collectionId,
        amount,
        status: OrderStatus.ACTIVE,
        owned_gift_id,
        createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
        updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
      };

      const orderRef = await db.collection("orders").add(orderData);

      return {
        success: true,
        orderId: orderRef.id,
        message: `Order created successfully with ${lockedAmount} TON locked (${
          buyerLockPercentage * 100
        }% of ${amount} TON order)`,
      };
    } catch (error) {
      console.error("Error creating order:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while creating order."
      );
    }
  }
);

export const makePurchaseAsBuyer = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID is required."
      );
    }

    try {
      const db = admin.firestore();
      const buyerId = context.auth.uid;

      // Get order
      const orderDoc = await db.collection("orders").doc(orderId).get();
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Order not found.");
      }

      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Check order status
      if (order.status !== "active") {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order is not available for purchase."
        );
      }

      // Check if this is a seller's order (has sellerId, no buyerId)
      if (!order.sellerId || order.buyerId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "This order is not available for buyer purchase."
        );
      }

      // Check if buyer is not the seller
      if (buyerId === order.sellerId) {
        throw new functions.https.HttpsError(
          "permission-denied",
          "You cannot purchase your own order."
        );
      }

      // Get buyer lock percentage from app config
      const config = await getAppConfig();
      const buyerLockPercentage =
        config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
      const buyerLockAmount = order.amount * buyerLockPercentage;

      // Check if buyer has sufficient balance
      const hasBalance = await hasAvailableBalance(buyerId, buyerLockAmount);
      if (!hasBalance) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          `Insufficient available balance to make purchase. Need ${buyerLockAmount} TON (${
            buyerLockPercentage * 100
          }% of order amount).`
        );
      }

      // Lock buyer's funds (fee will be applied when purchase is completed)
      await lockFunds(buyerId, buyerLockAmount);

      // Update order status and add buyer
      await db.collection("orders").doc(orderId).update({
        buyerId,
        status: "paid",
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Purchase completed successfully. ${buyerLockAmount} TON locked (${
          buyerLockPercentage * 100
        }% of ${order.amount} TON order)`,
      };
    } catch (error) {
      console.error("Error making purchase as buyer:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while making purchase."
      );
    }
  }
);

export const makePurchaseAsSeller = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID is required."
      );
    }

    try {
      const db = admin.firestore();
      const sellerId = context.auth.uid;

      // Get order
      const orderDoc = await db.collection("orders").doc(orderId).get();
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Order not found.");
      }

      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Check order status
      if (order.status !== "active") {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order is not available for purchase."
        );
      }

      // Check if this is a buyer's order (has buyerId, no sellerId)
      if (!order.buyerId || order.sellerId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "This order is not available for seller purchase."
        );
      }

      // Check if seller is not the buyer
      if (sellerId === order.buyerId) {
        throw new functions.https.HttpsError(
          "permission-denied",
          "You cannot purchase your own order."
        );
      }

      // Get seller lock percentage from app config
      const config = await getAppConfig();
      const sellerLockPercentage =
        config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;
      const sellerLockAmount = order.amount * sellerLockPercentage;

      // Check if seller has sufficient balance
      const hasBalance = await hasAvailableBalance(sellerId, sellerLockAmount);
      if (!hasBalance) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          `Insufficient available balance to make purchase. Need ${sellerLockAmount} TON (${
            sellerLockPercentage * 100
          }% of order amount).`
        );
      }

      // Lock seller's funds (fee will be applied when purchase is completed)
      await lockFunds(sellerId, sellerLockAmount);

      // Update order status and add seller
      await db.collection("orders").doc(orderId).update({
        sellerId,
        status: "paid",
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Purchase completed successfully. ${sellerLockAmount} TON locked (${
          sellerLockPercentage * 100
        }% of ${order.amount} TON order)`,
      };
    } catch (error) {
      console.error("Error making purchase as seller:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while making purchase."
      );
    }
  }
);

export const cancelOrder = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { orderId } = data;

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  try {
    const db = admin.firestore();
    const userId = context.auth.uid;

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check if order can be cancelled (active or paid status)
    if (order.status !== "active" && order.status !== "paid") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order cannot be cancelled in current status."
      );
    }

    // Check if user is either buyer or seller
    const isSeller = order.sellerId && userId === order.sellerId;
    const isBuyer = order.buyerId && userId === order.buyerId;

    if (!isSeller && !isBuyer) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only buyer or seller can cancel the order."
      );
    }

    // Get seller lock percentage from app config
    const config = await getAppConfig();
    const sellerLockPercentage =
      config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;
    const sellerLockedAmount = order.amount * sellerLockPercentage;

    // Case 1: Seller cancels order with no buyer (order status is "active")
    if (
      isSeller &&
      order.status === "active" &&
      !order.buyerId &&
      order.sellerId
    ) {
      // Apply fixed rejection fee to seller
      const fixedFee = FIXED_REJECTION_FEE_TON;
      await updateUserBalance(userId, -fixedFee, 0);
      await applyFeeToMarketplaceRevenue(
        fixedFee,
        "reject_order_spam_protection"
      );

      // Unlock seller's funds
      await unlockFunds(order.sellerId, sellerLockedAmount);

      // Update order status to cancelled
      await db.collection("orders").doc(orderId).update({
        status: OrderStatus.CANCELLED,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Order cancelled successfully. Fixed fee of ${fixedFee} TON applied for spam protection.`,
      };
    }

    // Case 2: Seller cancels order with buyer (order status is "paid")
    if (
      isSeller &&
      order.status === "paid" &&
      order.buyerId &&
      order.sellerId
    ) {
      const config = await getAppConfig();
      const rejectFeePercentage =
        config?.reject_order_fee ?? DEFAULT_REJECT_ORDER_FEE_BPS;

      // Seller loses their locked amount (20% of order)
      // From the order amount: reject_order_fee% goes to marketplace, remaining goes to buyer
      const marketplaceFee = (order.amount * rejectFeePercentage) / BPS_DIVISOR;
      const buyerCompensation = order.amount - marketplaceFee;

      // Transfer funds
      await spendLockedFunds(order.sellerId, sellerLockedAmount); // Seller loses their 20%
      await updateUserBalance(order.buyerId, buyerCompensation, 0); // Buyer gets order amount - marketplace fee
      await applyFeeToMarketplaceRevenue(
        marketplaceFee,
        "reject_order_penalty"
      );

      // Unlock buyer's funds (they get their money back + compensation)
      await unlockFunds(order.buyerId, order.amount);

      // Update order status to cancelled
      await db.collection("orders").doc(orderId).update({
        status: OrderStatus.CANCELLED,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Order cancelled. Seller lost ${sellerLockedAmount} TON. Buyer received ${buyerCompensation} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
      };
    }

    // Case 3: Buyer cancels order
    if (isBuyer && order.sellerId) {
      const config = await getAppConfig();
      const rejectFeePercentage =
        config?.reject_order_fee ?? DEFAULT_REJECT_ORDER_FEE_BPS;
      const buyerLockPercentage =
        config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
      const buyerLockedAmount = order.amount * buyerLockPercentage;

      // Buyer loses their locked amount (configurable % of order)
      // From the order amount: reject_order_fee% goes to marketplace, remaining goes to seller
      const marketplaceFee = (order.amount * rejectFeePercentage) / BPS_DIVISOR;
      const sellerCompensation = order.amount - marketplaceFee;

      // Transfer funds
      if (order.buyerId) {
        await spendLockedFunds(order.buyerId, buyerLockedAmount); // Buyer loses their locked amount
      }

      await updateUserBalance(order.sellerId, sellerCompensation, 0); // Seller gets order amount - marketplace fee
      await applyFeeToMarketplaceRevenue(
        marketplaceFee,
        "reject_order_penalty"
      );

      // Unlock seller's funds (they get their money back + compensation)
      await unlockFunds(order.sellerId, sellerLockedAmount);

      // Update order status to cancelled
      await db.collection("orders").doc(orderId).update({
        status: OrderStatus.CANCELLED,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Order cancelled. Buyer lost ${buyerLockedAmount} TON (${
          buyerLockPercentage * 100
        }% of order). Seller received ${sellerCompensation} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
      };
    }

    // Fallback (should not reach here)
    throw new functions.https.HttpsError(
      "internal",
      "Invalid rejection scenario."
    );
  } catch (error) {
    console.error("Error rejecting purchase:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while rejecting purchase."
    );
  }
});

export const completePurchase = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID is required."
      );
    }

    try {
      const db = admin.firestore();

      // Get order
      const orderDoc = await db.collection("orders").doc(orderId).get();
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Order not found.");
      }

      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Check order status - must be paid to complete
      if (order.status !== "paid") {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order must be in 'paid' status to complete."
        );
      }

      // Check if order has a buyer and seller
      if (!order.buyerId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order has no buyer."
        );
      }

      if (!order.sellerId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order has no seller."
        );
      }

      // Get buyer's referral information and app config
      const [buyerDoc, config] = await Promise.all([
        db.collection("users").doc(order.buyerId).get(),
        getAppConfig(),
      ]);
      const buyerData = buyerDoc.data();
      const referralId = buyerData?.referral_id;
      const buyerLockPercentage =
        config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
      const sellerLockPercentage =
        config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;
      const buyerLockedAmount = order.amount * buyerLockPercentage;
      const sellerLockedAmount = order.amount * sellerLockPercentage;

      // Apply purchase fee with referral logic
      const feeResult = await applyPurchaseFeeWithReferral(
        order.buyerId,
        order.amount,
        referralId
      );
      const netAmountToSeller = order.amount - feeResult.totalFee;

      // Spend buyer's locked funds (removes from both sum and locked)
      await spendLockedFunds(order.buyerId, buyerLockedAmount);

      // Unlock seller's funds
      await unlockFunds(order.sellerId, sellerLockedAmount);

      // Transfer net amount to seller
      await updateUserBalance(order.sellerId, netAmountToSeller, 0);

      // Update order status to fulfilled
      await db.collection("orders").doc(orderId).update({
        status: "fulfilled",
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Purchase completed successfully. Seller received ${netAmountToSeller} TON (${feeResult.totalFee} TON fee applied, ${feeResult.referralFee} TON to referrer)`,
        netAmountToSeller,
        feeAmount: feeResult.totalFee,
        referralFee: feeResult.referralFee,
        marketplaceFee: feeResult.marketplaceFee,
      };
    } catch (error) {
      console.error("Error completing purchase:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while completing purchase."
      );
    }
  }
);

export const getOrdersByProductId = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { productId } = data;

    if (!productId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Product ID is required."
      );
    }

    try {
      const db = admin.firestore();

      // Query orders by productId with status "paid" (ready for completion)
      const ordersQuery = await db
        .collection("orders")
        .where("productId", "==", productId)
        .where("status", "==", "paid")
        .get();

      const orders: OrderEntity[] = [];
      ordersQuery.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
      });

      return {
        success: true,
        orders,
        count: orders.length,
      };
    } catch (error) {
      console.error("Error getting orders by product ID:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while getting orders."
      );
    }
  }
);

export const getUserOrdersByBot = functions.https.onCall(async (data) => {
  const { userId, tgId, botToken } = data;

  if (!botToken) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Bot token is required."
    );
  }

  // Verify bot token
  if (!verifyBotToken(botToken)) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "Invalid bot token."
    );
  }

  try {
    const db = admin.firestore();
    let targetUserId = userId;

    // If tgId is provided, find user by Telegram ID
    if (tgId && !userId) {
      const userQuery = await db
        .collection("users")
        .where("tg_id", "==", tgId.toString())
        .limit(1)
        .get();

      if (userQuery.empty) {
        throw new functions.https.HttpsError(
          "not-found",
          "User not found with provided Telegram ID."
        );
      }

      targetUserId = userQuery.docs[0].id;
    }

    // If no userId or tgId provided, throw error
    if (!targetUserId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Either userId or tgId is required."
      );
    }

    // Get orders where user is either buyer or seller
    const [sellerOrdersQuery, buyerOrdersQuery] = await Promise.all([
      db.collection("orders").where("sellerId", "==", targetUserId).get(),
      db.collection("orders").where("buyerId", "==", targetUserId).get(),
    ]);

    const sellOrders: OrderEntity[] = [];
    const buyOrders: OrderEntity[] = [];

    // Add seller orders
    sellerOrdersQuery.forEach((doc) => {
      sellOrders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    // Add buyer orders
    buyerOrdersQuery.forEach((doc) => {
      buyOrders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    // Sort orders by creation date (newest first)
    const sortByDate = (a: OrderEntity, b: OrderEntity) => {
      const aTime = a.createdAt?.toMillis() || 0;
      const bTime = b.createdAt?.toMillis() || 0;
      return bTime - aTime;
    };

    sellOrders.sort(sortByDate);
    buyOrders.sort(sortByDate);

    // Combine all orders for backward compatibility
    const allOrders = [...sellOrders, ...buyOrders];
    allOrders.sort(sortByDate);

    return {
      success: true,
      orders: allOrders, // Keep for backward compatibility
      sellOrders,
      buyOrders,
      count: allOrders.length,
      sellOrdersCount: sellOrders.length,
      buyOrdersCount: buyOrders.length,
      userId: targetUserId,
    };
  } catch (error) {
    console.error("Error getting user orders by bot:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting user orders."
    );
  }
});

export const sendGiftToRelayerByBot = functions.https.onCall(async (data) => {
  const { orderId, botToken } = data;

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  if (!botToken) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Bot token is required."
    );
  }

  // Verify bot token
  if (!verifyBotToken(botToken)) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "Invalid bot token."
    );
  }

  try {
    const db = admin.firestore();

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check order status - must be paid to send gift to relayer
    if (order.status !== "paid") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order must be in 'paid' status to send gift to relayer."
      );
    }

    // Check if order has a buyer
    if (!order.buyerId) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order has no buyer."
      );
    }

    // Update order status to gift_sent_to_relayer
    await db.collection("orders").doc(orderId).update({
      status: "gift_sent_to_relayer",
      giftSentToRelayerAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: "Gift sent to relayer successfully. Buyer will be notified.",
      order: {
        id: order.id,
        number: order.number,
        status: "gift_sent_to_relayer",
      },
    };
  } catch (error) {
    console.error("Error sending gift to relayer:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while sending gift to relayer."
    );
  }
});

export const completePurchaseByBot = functions.https.onCall(async (data) => {
  const { orderId, botToken } = data;

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  if (!botToken) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Bot token is required."
    );
  }

  // Verify bot token
  if (!verifyBotToken(botToken)) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "Invalid bot token."
    );
  }

  try {
    const db = admin.firestore();

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check order status - must be paid to complete
    if (order.status !== "paid") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order must be in 'paid' status to complete."
      );
    }

    // Check if order has a buyer and seller
    if (!order.buyerId) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order has no buyer."
      );
    }

    if (!order.sellerId) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order has no seller."
      );
    }

    // Get buyer's referral information and app config
    const [buyerDoc, config] = await Promise.all([
      db.collection("users").doc(order.buyerId).get(),
      getAppConfig(),
    ]);
    const buyerData = buyerDoc.data();
    const referralId = buyerData?.referral_id;
    const buyerLockPercentage =
      config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
    const sellerLockPercentage =
      config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;
    const buyerLockedAmount = order.amount * buyerLockPercentage;
    const sellerLockedAmount = order.amount * sellerLockPercentage;

    // Apply purchase fee with referral logic
    const feeResult = await applyPurchaseFeeWithReferral(
      order.buyerId,
      order.amount,
      referralId
    );
    const netAmountToSeller = order.amount - feeResult.totalFee;

    // Spend buyer's locked funds (removes from both sum and locked)
    await spendLockedFunds(order.buyerId, buyerLockedAmount);

    // Unlock seller's funds
    await unlockFunds(order.sellerId, sellerLockedAmount);

    // Transfer net amount to seller
    await updateUserBalance(order.sellerId, netAmountToSeller, 0);

    // Update order status to fulfilled
    await db.collection("orders").doc(orderId).update({
      status: "fulfilled",
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: `Purchase completed successfully by bot. Seller received ${netAmountToSeller} TON (${feeResult.totalFee} TON fee applied, ${feeResult.referralFee} TON to referrer)`,
      netAmountToSeller,
      feeAmount: feeResult.totalFee,
      referralFee: feeResult.referralFee,
      marketplaceFee: feeResult.marketplaceFee,
      order: {
        id: order.id,
        number: order.number,
        status: "fulfilled",
      },
    };
  } catch (error) {
    console.error("Error completing purchase by bot:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while completing purchase."
    );
  }
});
