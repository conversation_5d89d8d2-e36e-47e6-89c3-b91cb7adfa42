import { Context } from "telegraf";
import dotenv from "dotenv";

dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;

export type TelegramBusinessMessage = {
  update_id: number;
  business_message: {
    business_connection_id: string;
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      username: string;
      language_code: string;
      is_premium: boolean;
    };
    chat: {
      id: number;
      first_name: string;
      username: string;
      type: "private" | "group" | "supergroup" | "channel";
    };
    date: number;
    unique_gift: {
      gift: {
        owned_gift_id: string;
        gift: {
          id: string;
          sticker: any;
          star_count: number;
          total_count?: number;
          remaining_count?: number;
          upgrade_star_count?: number;
        };
        date: number;
        is_saved: boolean;
        is_name_hidden: boolean;
        is_unique?: boolean; // Unique gifts can be transferred
      }; // Replace with exact shape if known
      origin: string;
    };
  };
};

const getSentGiftId = (ctx: Context) => {
  const businessMessage = (ctx.update as any).business_message;
  // TODO save owned_gift_id in DB when status will be changed to gift_sent_to_relayer
  return businessMessage.unique_gift.owned_gift_id;
};

export const businessConnectionMiddleware = async (
  ctx: Context,
  next: () => Promise<void>
) => {
  try {
    console.log("ctx.update", ctx.update);

    // @ts-expect-error
    if (!ctx.update?.business_message) {
      await next();
      return;
    }

    const businessMessage = (ctx.update as any).business_message;
    const businessConnectionId = businessMessage.business_connection_id;
    const senderId = businessMessage.from?.id;

    const giftIdToTransfer = getSentGiftId(ctx);

    if (!giftIdToTransfer) {
      console.log("No gift ID found in business message");
      return;
    }

    // Now send a new gift to the sender using the converted stars
    const sendGiftResponse = await fetch(
      `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          business_connection_id: businessConnectionId,
          new_owner_chat_id: businessMessage.chat.id,
          star_count: 25,
          owned_gift_id: giftIdToTransfer as string,
        }),
      }
    );

    const sendGiftResult = (await sendGiftResponse.json()) as {
      ok: boolean;
      description?: string;
      error_code?: number;
    };

    console.log("sendGiftResult", sendGiftResult);

    if (sendGiftResult.ok) {
      console.log(`Successfully sent gift back to user ${senderId}`);
    }

    await next();
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
};
