import { Context } from "telegraf";
import { sendGiftToRelayer } from "../firebase-service";
import { getUserSession } from "../services/session";
import { createEchoModeKeyboard } from "../utils/keyboards";
import { logMessageDetails } from "../utils/messageLogger";

export const handleMessage = async (ctx: Context) => {
  try {
    const userId = ctx.from?.id?.toString();
    if (!userId) return;

    const session = getUserSession(userId);
    const pendingOrderId = session?.pendingOrderId;
    const echoMode = true;

    // Handle echo mode first
    if (echoMode) {
      await handleEchoMode(ctx, userId);
      return;
    }

    // Check if this is a gift with order ID for relayer
    const orderIdFromMessage = extractOrderIdFromMessage(ctx);

    console.log("pendingOrderId from prev chat", pendingOrderId);
    console.log("orderIdFromMessage from current chat", orderIdFromMessage);

    if ((ctx.update as any).business_message.unique_gift.gift.owned_gift_id) {
      const finalOrderId = pendingOrderId || orderIdFromMessage;

      if (!finalOrderId) {
        console.log("No order ID found in message");
        return;
      }

      await handleGiftToRelayer(ctx, finalOrderId);
      return;
    }

    if (!pendingOrderId) {
      // No pending order and not in echo mode, ignore the message
      return;
    }

    // Handle order completion
    // await handleOrderCompletion(ctx, userId, pendingOrderId);
  } catch (error) {
    console.error("Error handling message:", error);
    ctx.reply("❌ An error occurred while processing your message.");
  }
};

const handleEchoMode = async (ctx: Context, userId: string) => {
  if (!ctx.message) return;

  logMessageDetails(userId, ctx.message);

  // Echo the message back to the user
  try {
    if (ctx.chat) {
      await ctx.copyMessage(ctx.chat.id);
    }
    ctx.reply(
      "✅ Gift echoed successfully!\n\n" +
        "📝 Check the console for logged data.\n\n" +
        "Send another gift or click Cancel to exit echo mode.",
      createEchoModeKeyboard()
    );
  } catch (error) {
    console.error("Error echoing message:", error);
    ctx.reply(
      "❌ Failed to echo the gift. This might be due to message type restrictions.\n\n" +
        "📝 However, the data was still logged to the console.",
      createEchoModeKeyboard()
    );
  }
};

// const handleOrderCompletion = async (
//   ctx: Context,
//   userId: string,
//   pendingOrderId: string
// ) => {
//   // Clear the pending order
//   clearUserSession(userId);

//   ctx.reply("🔄 Processing your gift and completing the order...");

//   try {
//     const result = await completePurchaseByBot(pendingOrderId);

//     if (result.success) {
//       ctx.reply(
//         "✅ Order Completed Successfully!\n\n" +
//           `📦 Order #${result.order.number}\n` +
//           `💰 Seller received: ${result.netAmountToSeller} TON\n` +
//           `💸 Fee applied: ${result.feeAmount} TON\n\n` +
//           "🎉 Thank you for using our marketplace!",
//         createOrderSuccessKeyboard()
//       );
//     } else {
//       throw new Error("Purchase completion failed");
//     }
//   } catch (completionError) {
//     console.error("Error completing purchase:", completionError);

//     // Return the gift to the user
//     ctx.reply(
//       "❌ Failed to complete the order. Your gift is being returned to you.\n\n" +
//         "Error: " +
//         (completionError as Error).message +
//         "\n\n" +
//         "Please try again later or contact support.",
//       createOrderErrorKeyboard()
//     );

//     // Forward the original message back to the user
//     if (ctx.message && ctx.from) {
//       try {
//         await ctx.forwardMessage(ctx.from.id);
//       } catch (forwardError) {
//         console.error("Error forwarding message back:", forwardError);
//         ctx.reply(
//           "⚠️ Unable to return your gift automatically. Please save it manually."
//         );
//       }
//     }
//   }
// };

const extractOrderIdFromMessage = (ctx: Context): string | null => {
  if (!ctx.message || !("text" in ctx.message)) return null;

  const text = ctx.message.text;
  if (!text) return null;

  // Look for order patterns: #123, Order #123, order 123, etc.
  const orderPatterns = [
    /(?:order\s*#?|#)(\d+)/i,
    /order\s+(\d+)/i,
    /^(\d+)$/, // Just a number
  ];

  for (const pattern of orderPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
};

const handleGiftToRelayer = async (ctx: Context, orderId: string) => {
  try {
    ctx.reply("🔄 Processing gift for relayer...");

    // Update order status to gift_sent_to_relayer
    const result = await sendGiftToRelayer(orderId!);

    if (result.success) {
      ctx.reply(
        `✅ Gift sent to relayer!\n\n` +
          "🎁 The buyer will be notified that their gift is ready.\n" +
          "📱 They can use the bot to receive their gift."
      );
    } else {
      ctx.reply(
        `❌ Failed to process gift for order #${orderId}.\n\n` +
          "Error: " +
          (result.message || "Unknown error") +
          "\n\n" +
          "Please try again or contact support."
      );
    }
  } catch (error) {
    console.error("Error handling gift to relayer:", error);
    ctx.reply(
      `❌ An error occurred while processing gift for order #${orderId}.\n\n` +
        "Please try again later or contact support."
    );
  }
};
